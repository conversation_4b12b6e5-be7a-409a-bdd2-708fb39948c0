#!/usr/bin/env python3
"""
Quick verification script to test the WebSocket path parameter fix.
This specifically tests that the connection handler receives both websocket and path parameters.
"""

import asyncio
import websockets
import json

async def test_direct_websocket():
    """Test direct connection to WebSocket server on port 8765"""
    
    hospital_id = "3854690459"
    websocket_url = f"ws://localhost:8765/ws/jambonz/{hospital_id}"
    
    print(f"Testing direct WebSocket connection...")
    print(f"URL: {websocket_url}")
    print("-" * 50)
    
    try:
        async with websockets.connect(
            websocket_url,
            subprotocols=["ws.jambonz.org"],
            timeout=5
        ) as websocket:
            print("✅ Connection established successfully!")
            print(f"   Remote address: {websocket.remote_address}")
            print(f"   Subprotocol: {websocket.subprotocol}")
            
            # Send a test message
            test_message = {
                "type": "session:new",
                "msgid": "test-direct-001",
                "call_sid": "test-call-direct-001",
                "from": "+1234567890",
                "to": "+9876543210"
            }
            
            print("\n📤 Sending test message...")
            await websocket.send(json.dumps(test_message))
            print("✅ Message sent successfully!")
            
            # Try to receive response (with timeout)
            print("\n📥 Waiting for response...")
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5)
                response_data = json.loads(response)
                print("✅ Received response:")
                print(f"   Type: {response_data.get('type', 'unknown')}")
                print(f"   Message ID: {response_data.get('msgid', 'none')}")
                
                if 'verbs' in response_data:
                    print(f"   Verbs: {len(response_data['verbs'])} verb(s)")
                    
            except asyncio.TimeoutError:
                print("⚠️  No response within timeout (this may be normal)")
            
            print("\n✅ Test completed successfully!")
            return True
            
    except ConnectionRefusedError:
        print("❌ Connection refused - WebSocket server not running on port 8765")
        return False
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

async def test_proxy_websocket():
    """Test proxy connection through FastAPI on port 8000"""
    
    hospital_id = "3854690459"
    proxy_url = f"ws://localhost:8000/ws/jambonz/{hospital_id}"
    
    print(f"\nTesting proxy WebSocket connection...")
    print(f"URL: {proxy_url}")
    print("-" * 50)
    
    try:
        async with websockets.connect(
            proxy_url,
            subprotocols=["ws.jambonz.org"],
            timeout=5
        ) as websocket:
            print("✅ Proxy connection established successfully!")
            
            # Send a test message through proxy
            test_message = {
                "type": "session:new",
                "msgid": "test-proxy-001",
                "call_sid": "test-call-proxy-001",
                "from": "+1234567890",
                "to": "+9876543210"
            }
            
            print("\n📤 Sending test message through proxy...")
            await websocket.send(json.dumps(test_message))
            print("✅ Message sent through proxy!")
            
            # Try to receive response
            print("\n📥 Waiting for response through proxy...")
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5)
                response_data = json.loads(response)
                print("✅ Received response through proxy:")
                print(f"   Type: {response_data.get('type', 'unknown')}")
                
            except asyncio.TimeoutError:
                print("⚠️  No response within timeout")
            
            print("\n✅ Proxy test completed successfully!")
            return True
            
    except Exception as e:
        print(f"❌ Proxy connection error: {e}")
        return False

async def main():
    """Main test function"""
    print("🔧 WebSocket Path Parameter Fix Verification")
    print("=" * 60)
    
    # Test direct WebSocket connection
    direct_success = await test_direct_websocket()
    
    # Small delay
    await asyncio.sleep(1)
    
    # Test proxy connection
    proxy_success = await test_proxy_websocket()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS:")
    print(f"Direct WebSocket (port 8765): {'✅ PASSED' if direct_success else '❌ FAILED'}")
    print(f"Proxy WebSocket (port 8000):  {'✅ PASSED' if proxy_success else '❌ FAILED'}")
    
    if direct_success and proxy_success:
        print("\n🎉 SUCCESS! Both connections work correctly.")
        print("The WebSocket path parameter fix is working!")
        print("\nYour Jambonz integration should now work with:")
        print("wss://your-ngrok-url.ngrok.io/ws/jambonz/3854690459")
        
    elif direct_success:
        print("\n⚠️  Direct WebSocket works, but proxy has issues.")
        print("Check FastAPI server logs for proxy errors.")
        
    elif proxy_success:
        print("\n⚠️  Proxy works, but direct WebSocket has issues.")
        print("This is unusual - check WebSocket server logs.")
        
    else:
        print("\n❌ Both tests failed.")
        print("Please check:")
        print("1. Server is running: python -m uvicorn voice_agent.main:app --host 0.0.0.0 --port 8000")
        print("2. Check server logs for errors")
        print("3. Verify ports 8000 and 8765 are not blocked")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
