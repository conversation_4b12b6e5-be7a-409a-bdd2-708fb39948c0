#!/usr/bin/env python3
"""
Test script to verify WebSocket server handles CORS and HTTP requests properly.
This script tests the production-ready WebSocket server implementation.
"""

import asyncio
import aiohttp
import websockets
import json
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

async def test_cors_preflight():
    """Test CORS preflight (OPTIONS) request"""
    print("🧪 Testing CORS preflight request...")
    
    try:
        async with aiohttp.ClientSession() as session:
            # Send OPTIONS request with CORS headers
            headers = {
                'Origin': 'https://example.com',
                'Access-Control-Request-Method': 'GET',
                'Access-Control-Request-Headers': 'Content-Type, Sec-WebSocket-Protocol'
            }
            
            async with session.options('http://localhost:8765/ws/jambonz/test_hospital', headers=headers) as response:
                print(f"   Status: {response.status}")
                print(f"   CORS Origin: {response.headers.get('Access-Control-Allow-Origin', 'Not set')}")
                print(f"   CORS Methods: {response.headers.get('Access-Control-Allow-Methods', 'Not set')}")
                print(f"   CORS Headers: {response.headers.get('Access-Control-Allow-Headers', 'Not set')}")
                
                if response.status == 200:
                    print("   ✅ CORS preflight handled correctly")
                    return True
                else:
                    print("   ❌ CORS preflight failed")
                    return False
                    
    except Exception as e:
        print(f"   ❌ CORS preflight test failed: {e}")
        return False

async def test_health_check():
    """Test health check endpoint"""
    print("🧪 Testing health check endpoint...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8765/health') as response:
                print(f"   Status: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    print(f"   Service: {data.get('service', 'Unknown')}")
                    print(f"   Status: {data.get('status', 'Unknown')}")
                    print(f"   Active Connections: {data.get('active_connections', 0)}")
                    print("   ✅ Health check working correctly")
                    return True
                else:
                    print("   ❌ Health check failed")
                    return False
                    
    except Exception as e:
        print(f"   ❌ Health check test failed: {e}")
        return False

async def test_http_to_websocket_endpoint():
    """Test HTTP request to WebSocket endpoint"""
    print("🧪 Testing HTTP request to WebSocket endpoint...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8765/ws/jambonz/test_hospital') as response:
                print(f"   Status: {response.status}")
                
                if response.status == 400:  # Bad Request is expected
                    data = await response.json()
                    print(f"   Message: {data.get('message', 'No message')}")
                    print("   ✅ HTTP to WebSocket endpoint handled correctly")
                    return True
                else:
                    print("   ❌ HTTP to WebSocket endpoint handling failed")
                    return False
                    
    except Exception as e:
        print(f"   ❌ HTTP to WebSocket endpoint test failed: {e}")
        return False

async def test_websocket_connection():
    """Test actual WebSocket connection"""
    print("🧪 Testing WebSocket connection...")
    
    try:
        # Connect to WebSocket with Jambonz subprotocol
        uri = "ws://localhost:8765/ws/jambonz/test_hospital"
        
        async with websockets.connect(uri, subprotocols=["ws.jambonz.org"]) as websocket:
            print(f"   Connected to: {uri}")
            print(f"   Subprotocol: {websocket.subprotocol}")
            
            # Send a test message
            test_message = {
                "type": "session:new",
                "msgid": "test-123",
                "call_sid": "test-call-123",
                "from": "+1234567890",
                "to": "+0987654321"
            }
            
            await websocket.send(json.dumps(test_message))
            print("   📤 Sent test message")
            
            # Wait for response (with timeout)
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print("   📥 Received response")
                print("   ✅ WebSocket connection working correctly")
                return True
            except asyncio.TimeoutError:
                print("   ⚠️  No response received (this might be expected if handlers aren't fully configured)")
                print("   ✅ WebSocket connection established successfully")
                return True
                
    except Exception as e:
        print(f"   ❌ WebSocket connection test failed: {e}")
        return False

async def test_404_endpoint():
    """Test 404 for non-existent endpoints"""
    print("🧪 Testing 404 for non-existent endpoint...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8765/nonexistent') as response:
                print(f"   Status: {response.status}")
                
                if response.status == 404:
                    data = await response.json()
                    print(f"   Error: {data.get('error', 'No error message')}")
                    print("   ✅ 404 handling working correctly")
                    return True
                else:
                    print("   ❌ 404 handling failed")
                    return False
                    
    except Exception as e:
        print(f"   ❌ 404 test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🚀 Starting WebSocket server CORS and HTTP handling tests")
    print("=" * 60)
    
    # Check if server is running
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8765/health', timeout=aiohttp.ClientTimeout(total=2)) as response:
                if response.status != 200:
                    raise Exception("Server not responding")
    except Exception:
        print("❌ WebSocket server is not running on localhost:8765")
        print("   Please start the server first:")
        print("   python -m voice_agent.main")
        return False
    
    print("✅ Server is running, starting tests...\n")
    
    # Run all tests
    tests = [
        ("CORS Preflight", test_cors_preflight),
        ("Health Check", test_health_check),
        ("HTTP to WebSocket", test_http_to_websocket_endpoint),
        ("WebSocket Connection", test_websocket_connection),
        ("404 Handling", test_404_endpoint)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        result = await test_func()
        results.append((test_name, result))
        print()
    
    # Summary
    print("=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:.<30} {status}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! The server is handling CORS and HTTP requests correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the server implementation.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
